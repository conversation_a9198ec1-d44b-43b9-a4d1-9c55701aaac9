/* Walk-in Sales POS System Styles - Redesigned for Professional Use */

.walk-in-container {
    max-width: 100vw;
    margin: 0;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* Header - Consistent with Staff Dashboard */
.walk-in-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 28px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3498db;
}

.walk-in-header h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.walk-in-header h1 i {
    color: #3498db;
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.header-actions .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.header-actions .btn-warning {
    background: #f39c12;
    color: white;
}

.header-actions .btn-warning:hover {
    background: #e67e22;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
}

.header-actions .btn-success {
    background: #27ae60;
    color: white;
}

.header-actions .btn-success:hover {
    background: #229954;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

/* Main Layout - Fixed for Proper Visibility */
.walk-in-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
    height: calc(100vh - 140px);
    align-items: start;
    max-width: 100%;
    overflow: visible; /* Allow pagination to be visible */
}

/* Product Panel - Clean Professional Design */
.product-panel {
    background: #fff;
    border-radius: 10px;
    padding: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: visible; /* Allow pagination to be visible */
    border: 1px solid #e9ecef;
    height: calc(100vh - 120px); /* Fixed height to ensure pagination is visible */
}

/* Search Section - Simplified and Clean */
.search-section {
    padding: 24px;
    background: #fff;
    border-bottom: 1px solid #e9ecef;
}

.search-bar {
    position: relative;
    margin-bottom: 20px;
}

.search-bar input {
    width: 100%;
    padding: 14px 20px 14px 50px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    background: #fff;
    transition: all 0.2s ease;
    font-family: inherit;
}

.search-bar input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-bar input::placeholder {
    color: #6c757d;
    font-weight: 400;
}

.search-bar::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 1;
    font-size: 1rem;
}

.search-btn {
    display: none; /* Remove redundant search button */
}

/* Filter Buttons - Professional Style */
.quick-filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #dee2e6;
    background: #fff;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #495057;
    min-width: 80px;
    text-align: center;
}

.filter-btn:hover {
    border-color: #3498db;
    background: #f8f9fa;
    color: #3498db;
}

.filter-btn.active {
    background: #3498db;
    border-color: #3498db;
    color: white;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}

/* Brand Filtering Section - Homepage Style */
.brand-filters-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.filter-section-title {
    font-size: 0.9em;
    font-weight: 600;
    color: #555;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Brand Categories Container - Match Homepage Layout */
.brand-filters-section .categories {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    max-width: 100%;
    overflow-x: auto;
    padding: 10px 0;
}

/* Brand Category Cards - Exact Homepage Styling */
.brand-category {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px;
    padding: 35px;
    border: 3px solid #ccc;
    border-radius: 10px;
    transition: transform 0.3s, background-color 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    background-color: #fff;
}

.brand-category:hover, .brand-category:focus-within {
    transform: scale(1.05);
    background-color: #f0f0f0;
}

.brand-category.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.brand-category.active:hover {
    background-color: #0056b3;
}

.brand-category .category-icon {
    max-width: 90px;
    height: auto;
    margin-bottom: 10px;
}

.brand-category h4 {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    text-align: center;
}

.brand-category.active h4 {
    color: #fff;
}

/* Products Grid - Professional POS Layout - 2 Rows x 4 Columns */
.products-grid {
    flex: 1;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* Exactly 4 columns for 4 products per row */
    grid-template-rows: repeat(2, minmax(350px, 1fr)); /* Increased minimum height for better discount display */
    gap: 20px; /* Increased gap for better spacing */
    padding: 24px;
    padding-bottom: 120px; /* Extra space for sticky pagination */
    background: #fff;
    min-height: 720px; /* Increased minimum height for 2 rows with larger cards */
}

/* Product Cards - Clean Professional Design */
.product-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0;
    background: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 350px; /* Increased height for better discount display */
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
    transform: translateY(-2px);
}

.product-card.selected {
    border-color: #27ae60;
    background: #f8fff9;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.product-card.out-of-stock {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
    background: #f8f9fa;
}

.product-card.out-of-stock::after {
    content: 'OUT OF STOCK';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
    z-index: 2;
}

.product-card.out-of-stock:hover {
    border-color: #e9ecef;
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Product Card Content - Professional Layout */
.product-image {
    width: 100%;
    height: 160px;
    object-fit: contain;
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.product-info {
    padding: 20px;
    flex: 1; /* Take remaining space */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-info h4 {
    font-size: 1.1rem; /* Slightly larger font */
    font-weight: 600;
    margin: 0 0 12px 0;
    color: #2c3e50;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 3.2rem; /* Increased minimum height */
}

.product-price {
    font-size: 1.6rem; /* Even larger price for better visibility in bigger cards */
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 12px;
}

.product-stock {
    font-size: 0.9rem; /* Slightly larger stock text */
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    padding: 8px 12px; /* Increased padding */
    border-radius: 6px;
    width: fit-content;
    border: 1px solid #e9ecef;
    margin-top: auto; /* Push to bottom of card */
}

.stock-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
}

.stock-indicator.low {
    background: #ffc107;
}

.stock-indicator.out {
    background: #dc3545;
}

/* Add to Cart Button */
.product-card .add-to-cart-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.product-card:hover .add-to-cart-btn {
    opacity: 1;
    transform: scale(1);
}

.product-card .add-to-cart-btn:hover {
    background: #2980b9;
    transform: scale(1.1);
}

.loading-spinner {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px;
    color: #6b7280;
    font-size: 1.1rem;
}

/* Cart Panel - Fixed Visibility */
.cart-panel {
    background: #fff;
    border-radius: 10px;
    padding: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: calc(100vh - 140px);
    overflow-y: auto; /* Allow scrolling to see QR code */
    border: 1px solid #e9ecef;
    width: 100%;
    min-width: 380px;
}

/* Cart Header - Clean Professional Style */
.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #2c3e50;
    color: white;
    margin: 0;
    border-bottom: 3px solid #3498db;
}

.cart-header h3 {
    margin: 0;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-header h3 i {
    font-size: 1.1rem;
}

.cart-count {
    background: #3498db;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

/* Cart Items - Fixed Scrolling */
.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    min-height: 200px;
    max-height: 300px;
    background: #f8f9fa;
}

/* Empty Cart State - Professional */
.empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    background: white;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
    margin: 20px 0;
}

.empty-cart i {
    font-size: 2.5rem;
    margin-bottom: 16px;
    color: #adb5bd;
}

.empty-cart p {
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #495057;
}

.empty-cart small {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Cart Items - Clean Professional Style */
.cart-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 12px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.cart-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

/* Cart Item Components */
.cart-item-image {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 6px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    margin-right: 12px;
}

.cart-item-info {
    flex: 1;
    margin-right: 12px;
}

.cart-item-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 4px;
    line-height: 1.3;
}

.cart-item-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.85rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #495057;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.quantity-input {
    width: 50px;
    height: 28px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
    font-size: 0.85rem;
    font-weight: 600;
}

.remove-item-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #dc3545;
    background: #dc3545;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-item-btn:hover {
    background: #c82333;
    border-color: #c82333;
}

.cart-item-image {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 6px;
    margin-right: 12px;
    background: white;
}

.cart-item-info {
    flex: 1;
    margin-right: 12px;
}

.cart-item-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
    line-height: 1.2;
}

.cart-item-price {
    font-size: 0.875rem;
    color: #059669;
    font-weight: 500;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 4px;
    font-size: 0.875rem;
}

.remove-item-btn {
    color: #ef4444;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.remove-item-btn:hover {
    background: #fee2e2;
}

/* Cart Summary - Fixed Layout */
.cart-summary {
    margin: 0 16px 16px 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 0.9rem;
    color: #64748b;
}

.summary-row span:last-child {
    font-weight: 600;
    color: #1e293b;
}

.summary-row.total {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1e293b;
    border-top: 2px solid #e2e8f0;
    padding-top: 12px;
    margin-top: 12px;
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin-left: -20px;
    margin-right: -20px;
    margin-bottom: -20px;
}

.summary-row.total span:last-child {
    color: #059669;
    font-size: 1.25rem;
}

/* Customer Section - Fixed Layout */
.customer-section {
    margin: 0 16px 16px 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.customer-section h4 {
    margin: 0 0 16px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-section h4 i {
    color: #667eea;
}

.form-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.form-row input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-row input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Payment Section - Fixed Layout */
.payment-section {
    margin: 0 16px 16px 16px;
    padding: 16px;
    padding-bottom: 40px; /* Extra bottom padding to ensure QR code is fully visible */
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.payment-section h4 {
    margin: 0 0 16px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-section h4 i {
    color: #667eea;
}

.payment-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 20px;
    min-height: 80px; /* Ensure enough height for both buttons */
}

.payment-btn {
    padding: 16px 12px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    min-height: 70px; /* Ensure buttons are tall enough to be visible */
    color: #374151; /* Default text color for inactive buttons */
}

.payment-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.payment-btn:hover::before {
    left: 100%;
}

.payment-btn:hover {
    border-color: #667eea;
    background: #f8faff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.payment-btn.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.payment-btn i {
    font-size: 1.5rem;
    margin-bottom: 4px;
    color: #667eea; /* Ensure icons are always visible with blue color */
    display: block; /* Ensure icons are always displayed */
}

.payment-btn.active i {
    color: white; /* White color for active button icons */
}

.payment-btn span {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151; /* Dark color for inactive button text */
}

.payment-btn.active span {
    color: white; /* White color for active button text */
}

.payment-details {
    display: none;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.payment-details.active {
    display: block;
}

.qr-container {
    text-align: center;
    margin-bottom: 30px; /* Increased bottom margin to ensure QR code is fully visible */
    padding-bottom: 20px; /* Additional padding for safety */
}

.qr-placeholder {
    width: 150px;
    height: 150px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: #9ca3af;
}

.qr-placeholder i {
    font-size: 2rem;
    margin-bottom: 8px;
}

.payment-instruction {
    text-align: center;
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
}

.cash-input {
    margin-bottom: 12px;
}

.cash-input label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.cash-input input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
}

.change-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #ecfdf5;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    font-weight: 500;
}

.change-amount {
    color: #059669;
    font-size: 1.125rem;
    font-weight: 700;
}

/* Action Buttons - Fixed Layout */
.action-buttons {
    padding: 0 16px 16px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn-lg {
    padding: 16px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-lg::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-lg:hover::before {
    left: 100%;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

/* Payment Details Sections */
.payment-details {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    display: none;
}

.payment-details.active {
    display: block;
}

.qr-placeholder {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.cash-input-group {
    margin-bottom: 12px;
}

.cash-input-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.cash-input-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
}

.change-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-top: 12px;
}

.change-amount {
    font-weight: 700;
    font-size: 1.1rem;
}

/* Professional Responsive Design - Fixed Cart Visibility */
@media (max-width: 1400px) {
    .walk-in-layout {
        grid-template-columns: 1fr 380px;
    }
}

@media (max-width: 1200px) {
    .walk-in-layout {
        grid-template-columns: 1fr 360px;
    }

    .products-grid {
        grid-template-columns: repeat(4, 1fr); /* Maintain 4 columns on tablet */
        grid-template-rows: repeat(2, minmax(180px, 1fr));
        gap: 14px;
        min-height: 380px;
    }

    .cart-panel {
        min-width: 340px;
    }
}

@media (max-width: 992px) {
    .walk-in-layout {
        grid-template-columns: 1fr;
        height: auto;
        gap: 20px;
    }

    .cart-panel {
        order: -1;
        max-height: 400px;
        position: relative;
        top: 0;
    }

    .products-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 columns on smaller tablets */
        grid-template-rows: repeat(3, minmax(160px, 1fr)); /* 3 rows to accommodate 8 products */
        gap: 12px;
        min-height: 500px;
    }
}

@media (max-width: 768px) {
    .walk-in-container {
        padding: 16px;
    }

    .walk-in-header {
        padding: 16px 20px;
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .header-actions .btn {
        flex: 1;
        max-width: 150px;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns on mobile landscape */
        grid-template-rows: repeat(4, minmax(140px, 1fr)); /* 4 rows to accommodate 8 products */
        gap: 14px;
        padding: 16px;
        min-height: 580px;
    }

    .search-section {
        padding: 20px;
    }

    .quick-filters {
        flex-direction: column;
        gap: 8px;
    }

    .filter-btn {
        width: 100%;
        text-align: center;
    }

    .cart-header {
        padding: 16px 20px;
    }

    .cart-items {
        padding: 16px;
    }

    .customer-section,
    .payment-section {
        margin: 0 16px 16px 16px;
        padding: 16px;
    }

    .action-buttons {
        padding: 0 16px 16px 16px;
    }

    .payment-methods {
        grid-template-columns: 1fr 1fr; /* Keep 2 columns even on mobile */
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .walk-in-container {
        padding: 12px;
    }

    .products-grid {
        grid-template-columns: 1fr 1fr; /* 2 columns on small mobile */
        grid-template-rows: repeat(4, minmax(120px, 1fr)); /* 4 rows to accommodate 8 products */
        gap: 10px;
        padding: 12px;
        min-height: 500px;
    }

    .product-card .product-info h4 {
        font-size: 0.9rem;
        -webkit-line-clamp: 3;
    }

    .product-price {
        font-size: 1.1rem;
    }

    .add-to-cart-btn {
        width: 36px !important;
        height: 36px !important;
        bottom: 16px !important;
        right: 16px !important;
    }
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    background: white;
    border-left: 4px solid #10b981;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 300px;
    /* Removed CSS animation to prevent conflict with JavaScript animation */
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.warning {
    border-left-color: #f59e0b;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Success Modal Styles */
.success-icon {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 16px;
}

.sale-summary {
    background: #f8fafc;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
    text-align: left;
}

/* Professional Bootstrap Pagination Styles - Sticky Bottom */
.pagination-container {
    position: sticky;
    bottom: 0;
    margin-top: auto; /* Push to bottom */
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    background: #fff;
    flex-shrink: 0; /* Prevent shrinking */
    min-height: 80px; /* Ensure minimum space for pagination */
    z-index: 10; /* Ensure it stays on top */
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow for separation */
}

/* Bootstrap pagination customization */
.pagination {
    margin-bottom: 0;
}

.pagination .page-item .page-link {
    padding: 8px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    color: #495057;
    background-color: white;
    margin: 0 2px;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 40px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-item.active .page-link {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}

.pagination .page-item:not(.active):not(.disabled) .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #3498db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
}

/* Pagination info styling */
.pagination-info {
    font-size: 0.85rem;
    color: #6c757d;
    text-align: center;
    margin: 0;
    font-weight: 500;
}

/* Mobile pagination adjustments */
@media (max-width: 768px) {
    .pagination .page-item .page-link {
        padding: 6px 10px;
        font-size: 0.8rem;
        min-width: 35px;
    }

    .pagination-info {
        font-size: 0.8rem;
    }
}

/* Invoice Styles */
.invoice-header {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e5e7eb;
}

.invoice-header h2 {
    margin: 0 0 16px 0;
    color: #1f2937;
}

.invoice-details {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.customer-details {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
}

.customer-details h4 {
    margin: 0 0 12px 0;
    color: #374151;
}

.invoice-items {
    margin-bottom: 24px;
}

.invoice-items table {
    width: 100%;
    border-collapse: collapse;
}

.invoice-items th,
.invoice-items td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.invoice-items th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
}

.invoice-items tfoot th {
    background: #1f2937;
    color: white;
    font-size: 1.125rem;
}

.invoice-footer {
    text-align: center;
    padding-top: 24px;
    border-top: 2px solid #e5e7eb;
    color: #6b7280;
}

/* Print Styles */
@media print {
    .walk-in-container {
        background: white;
    }

    .walk-in-header,
    .product-panel,
    .cart-panel .cart-header,
    .customer-section,
    .payment-section,
    .action-buttons {
        display: none !important;
    }

    .modal-header,
    .modal-footer {
        display: none !important;
    }

    .modal-body {
        padding: 0 !important;
    }

    .invoice-header,
    .customer-details,
    .invoice-items,
    .invoice-footer {
        page-break-inside: avoid;
    }
}

/* Customer Information Styles */
.customer-info-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.customer-name-display {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.customer-details-content {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #3498db;
    margin-top: 8px;
}

.customer-details-content div {
    margin-bottom: 6px;
    color: #6c757d;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-details-content div:last-child {
    margin-bottom: 0;
}

.customer-details-content i {
    width: 16px;
    color: #3498db;
}

/* Customer Modal Styles */
.modal-lg .modal-body {
    padding: 24px;
}

.modal-body .alert {
    border-radius: 8px;
    border: none;
    background: #e3f2fd;
    color: #1565c0;
    border-left: 4px solid #2196f3;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-label i {
    color: #3498db;
    width: 16px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    padding: 10px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-check {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
}

.form-check-input:checked {
    background-color: #3498db;
    border-color: #3498db;
}

.modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Discount Styles */
.discount-badge {
    background: #10b981;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7rem;
    margin-left: 8px;
    font-weight: 600;
}

.original-price {
    text-decoration: line-through;
    color: #6b7280;
    margin-right: 8px;
}

.discounted-price {
    color: #10b981;
    font-weight: 600;
}

.discount-info {
    background: #10b981;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-left: 6px;
}

.discount-summary {
    border-top: 1px solid #e5e7eb;
    padding-top: 8px;
    margin-bottom: 8px;
}

.discount-row {
    color: #10b981;
    font-weight: 600;
}

.discount-total {
    color: #10b981 !important;
    font-weight: 600;
}

.original-total {
    text-decoration: line-through;
    color: #6b7280;
}

/* Product Card Discount Styles */
.discounted-product {
    border: 2px solid #10b981 !important;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2) !important;
    position: relative;
}

.discount-badge-corner {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #10b981;
    color: white;
    padding: 6px 10px; /* Increased padding for better visibility */
    border-radius: 12px;
    font-size: 0.8rem; /* Increased font size */
    font-weight: 700;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.price-with-discount {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.price-with-discount .original-price {
    text-decoration: line-through;
    color: #6b7280;
    font-size: 1.0rem; /* Increased for better visibility */
    font-weight: 400;
}

.price-with-discount .discounted-price {
    color: #10b981;
    font-size: 1.4rem; /* Increased for better visibility */
    font-weight: 700;
}

.discount-percentage {
    background: #10b981;
    color: white;
    padding: 4px 8px; /* Increased padding for better visibility */
    border-radius: 8px;
    font-size: 0.8rem; /* Increased font size */
    font-weight: 600;
    align-self: flex-start;
    margin-top: 2px;
}

.regular-price {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Discount Filter Button */
.discount-filter {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    border: 2px solid #10b981 !important;
    font-weight: 600 !important;
}

.discount-filter:hover {
    background: linear-gradient(135deg, #059669, #047857) !important;
    border-color: #059669 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3) !important;
}

.discount-filter.active {
    background: linear-gradient(135deg, #059669, #047857) !important;
    border-color: #047857 !important;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
}
