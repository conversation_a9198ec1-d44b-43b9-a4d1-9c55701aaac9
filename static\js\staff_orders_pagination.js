document.addEventListener('DOMContentLoaded', () => {
    const ordersTableBody = document.querySelector('tbody');
    const paginationContainer = document.getElementById('pagination');
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const dateFilter = document.getElementById('date-filter');
    // Apply filters button removed - filters now work immediately

    let currentPage = 1;
    const pageSize = 10;

    function fetchOrders(page = 1) {
        const search = searchInput.value.trim();
        const status = statusFilter.value;
        const date = dateFilter.value;

        const params = new URLSearchParams({
            page: page,
            page_size: pageSize,
            search: search,
            status: status,
            date: date
        });

        fetch(`/auth/staff/api/orders?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                console.log('API response:', data);
                if (data.success) {
                    renderOrders(data.orders);
                    renderPagination(data.total_orders, page);
                    currentPage = page; // Update current page
                } else {
                    ordersTableBody.innerHTML = '<tr><td colspan="9">Failed to load orders.</td></tr>';
                    paginationContainer.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('Error fetching orders:', error);
                ordersTableBody.innerHTML = '<tr><td colspan="9">Error loading orders.</td></tr>';
                paginationContainer.innerHTML = '';
            });
    }

    // Make fetchOrders available globally for immediate filtering
    window.fetchOrdersFromPagination = fetchOrders;

    function renderOrders(orders) {
        const mobileOrdersList = document.getElementById('mobile-orders-list');
        const isMobile = window.innerWidth < 768;

        if (!orders || orders.length === 0) {
            ordersTableBody.innerHTML = '<tr><td colspan="9">No orders found.</td></tr>';
            if (mobileOrdersList) mobileOrdersList.innerHTML = '<p class="text-center">No orders found.</p>';
            return;
        }

        ordersTableBody.innerHTML = '';
        if (mobileOrdersList) mobileOrdersList.innerHTML = '';

        // Store current data for responsive re-rendering
        window.currentOrdersData = orders;

        orders.forEach((order, index) => {
            const serialNumber = (currentPage - 1) * pageSize + index + 1;

            // Desktop table row
            const tr = document.createElement('tr');

            // Create type badge
            const typeBadge = getOrderTypeBadge(order.type || 'order');

            // Create status badges
            const mainStatusBadge = getMainStatusBadge(order.status);

            // Create action buttons
            const actionButtons = getOrderActionButtons(order);

            tr.innerHTML = `
                <td>${typeBadge}</td>
                <td>${order.id}</td>
                <td>${order.first_name} ${order.last_name}</td>
                <td>${order.order_date}</td>
                <td>$${order.total.toFixed(2)}</td>
                <td>${order.payment_method || 'QR Payment'}</td>
                <td style="text-align: center;">${mainStatusBadge}</td>
                <td>${actionButtons}</td>
            `;
            ordersTableBody.appendChild(tr);

            // Mobile card
            if (mobileOrdersList) {
                const card = document.createElement('div');
                card.className = 'mobile-card';

                const typeBadge = getOrderTypeBadge(order.type || 'order');
                const mainStatusBadge = getMainStatusBadge(order.status);

                card.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                        <div>
                            <div style="margin-bottom: 5px;">${typeBadge}</div>
                            <p><strong>Order #${order.id}</strong></p>
                            <p><strong>Customer:</strong> ${order.first_name} ${order.last_name}</p>
                            <p><strong>Date:</strong> ${order.order_date}</p>
                            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
                            <p><strong>Payment:</strong> ${order.payment_method || 'QR Payment'}</p>
                            <p><strong>Status:</strong> ${mainStatusBadge}</p>
                        </div>
                    </div>
                    <div class="action-buttons" style="display: flex; gap: 5px; flex-wrap: wrap;">
                        <a href="/auth/staff/orders/${order.id}/details" class="btn btn-primary btn-sm">Details</a>
                        ${order.status.toLowerCase() !== 'cancelled' && order.approval_status !== 'Approved' ?
                            `<button type="button" class="btn btn-success btn-sm" onclick="approveOrder(${order.id})">Confirm</button>
                             <button type="button" class="btn btn-danger btn-sm" onclick="rejectOrder(${order.id})">Reject</button>` :
                            ''
                        }
                    </div>
                `;
                mobileOrdersList.appendChild(card);
            }
        });

        // Status change handling removed - orders are automatically managed
    }

    function renderPagination(totalOrders, currentPage) {
        const totalPages = Math.ceil(totalOrders / pageSize);
        paginationContainer.innerHTML = '';

        if (totalPages <= 1) return;

        const isMobile = window.innerWidth < 768;
        const maxButtons = isMobile ? 3 : 5;

        // First button
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item' + (currentPage === 1 ? ' disabled' : '');
        firstLi.innerHTML = `<a class="page-link" href="#" aria-label="First" style="padding: ${isMobile ? '6px 10px' : '8px 12px'}; font-size: ${isMobile ? '0.9rem' : '1rem'};">First</a>`;
        firstLi.addEventListener('click', e => {
            e.preventDefault();
            if (currentPage > 1) fetchOrders(1);
        });
        paginationContainer.appendChild(firstLi);

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = 'page-item' + (currentPage === 1 ? ' disabled' : '');
        prevLi.innerHTML = `<a class="page-link" href="#" aria-label="Previous" style="padding: ${isMobile ? '6px 10px' : '8px 12px'}; font-size: ${isMobile ? '0.9rem' : '1rem'};">«</a>`;
        prevLi.addEventListener('click', e => {
            e.preventDefault();
            if (currentPage > 1) fetchOrders(currentPage - 1);
        });
        paginationContainer.appendChild(prevLi);

        // Calculate page range
        let startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
        let endPage = Math.min(totalPages, startPage + maxButtons - 1);
        if (endPage === totalPages) {
            startPage = Math.max(1, totalPages - maxButtons + 1);
        }

        // Page number buttons
        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = 'page-item' + (i === currentPage ? ' active' : '');
            li.innerHTML = `<a class="page-link" href="#" style="padding: ${isMobile ? '6px 10px' : '8px 12px'}; font-size: ${isMobile ? '0.9rem' : '1rem'};">${i}</a>`;
            li.addEventListener('click', e => {
                e.preventDefault();
                fetchOrders(i);
            });
            paginationContainer.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = 'page-item' + (currentPage === totalPages ? ' disabled' : '');
        nextLi.innerHTML = `<a class="page-link" href="#" aria-label="Next" style="padding: ${isMobile ? '6px 10px' : '8px 12px'}; font-size: ${isMobile ? '0.9rem' : '1rem'};">»</a>`;
        nextLi.addEventListener('click', e => {
            e.preventDefault();
            if (currentPage < totalPages) fetchOrders(currentPage + 1);
        });
        paginationContainer.appendChild(nextLi);

        // Last button
        const lastLi = document.createElement('li');
        lastLi.className = 'page-item' + (currentPage === totalPages ? ' disabled' : '');
        lastLi.innerHTML = `<a class="page-link" href="#" aria-label="Last" style="padding: ${isMobile ? '6px 10px' : '8px 12px'}; font-size: ${isMobile ? '0.9rem' : '1rem'};">Last</a>`;
        lastLi.addEventListener('click', e => {
            e.preventDefault();
            if (currentPage < totalPages) fetchOrders(totalPages);
        });
        paginationContainer.appendChild(lastLi);
    }

    // updateOrderStatus function removed - orders are automatically managed

    // Apply filters button removed - filters now trigger immediately via onchange events

    // Responsive handling
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            // Re-render current data to adjust for screen size changes
            if (window.currentOrdersData) {
                renderOrders(window.currentOrdersData);
            }
        }, 100);
    });

    // Initial fetch
    fetchOrders(currentPage);
});

// Helper functions for status badges and action buttons
function getOrderTypeBadge(type) {
    if (type === 'order') {
        return `<span style="background-color: #007bff; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">Order</span>`;
    } else {
        return `<span style="background-color: #fd7e14; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">Pre-Order</span>`;
    }
}

function getMainStatusBadge(status) {
    const statusColors = {
        'pending': '#ffc107',
        'completed': '#28a745',
        'cancelled': '#dc3545'
    };
    const color = statusColors[status.toLowerCase()] || '#6c757d';
    return `<span style="background-color: ${color}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;
}

function getApprovalStatusBadge(approvalStatus, orderStatus) {
    // Don't show approval status for cancelled orders
    if (orderStatus && orderStatus.toLowerCase() === 'cancelled') {
        return '<span style="color: #6c757d; font-size: 12px;">N/A</span>';
    }

    if (!approvalStatus) {
        return '<span style="color: #6c757d; font-size: 12px;">N/A</span>';
    }

    const approvalColors = {
        'Pending Approval': '#ffc107',
        'Approved': '#28a745',
        'Rejected': '#dc3545'
    };
    const color = approvalColors[approvalStatus] || '#6c757d';
    return `<span style="background-color: ${color}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">${approvalStatus}</span>`;
}

function getOrderActionButtons(order) {
    let buttons = '<div style="display: flex; flex-direction: column; gap: 5px; min-width: 120px;">';

    // Show Details button - different handling for pre-orders vs regular orders
    if (order.type === 'preorder') {
        // For pre-orders, use JavaScript function to show pre-order details modal
        buttons += `<button type="button" class="btn btn-primary btn-sm" onclick="showPreOrderDetails(${order.id})" style="width: 100%; text-align: center;">Details</button>`;
    } else {
        // For regular orders, use the standard order details page
        buttons += `<a href="/auth/staff/orders/${order.id}/details" class="btn btn-primary btn-sm" style="width: 100%; text-align: center;">Details</a>`;
    }

    // Add approval buttons only for regular orders that need approval (not cancelled, not already approved, not pre-orders)
    console.log('Order status:', order.status, 'Approval status:', order.approval_status, 'Type:', order.type, 'User role:', window.userRole);
    if (order.status.toLowerCase() !== 'cancelled' &&
        order.approval_status !== 'Approved' &&
        order.type !== 'preorder') {  // Exclude pre-orders from approval workflow
        console.log('Adding approval buttons for order:', order.id);
        buttons += `<button type="button" class="btn btn-success btn-sm" onclick="approveOrder(${order.id})" style="width: 100%;">Confirm</button>`;
        buttons += `<button type="button" class="btn btn-danger btn-sm" onclick="rejectOrder(${order.id})" style="width: 100%;">Reject</button>`;
    } else {
        console.log('Skipping approval buttons for order:', order.id, '- Status:', order.status, 'Approval:', order.approval_status, 'Type:', order.type);
    }



    buttons += '</div>';
    return buttons;
}

// Function to show pre-order details modal
function showPreOrderDetails(preorderId) {
    // Fetch pre-order details
    fetch(`/auth/staff/api/pre_order/${preorderId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPreOrderDetailsModal(data.pre_order);
            } else {
                alert('Error loading pre-order details: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading pre-order details');
        });
}

// Function to display pre-order details modal
function displayPreOrderDetailsModal(preorder) {
    const modalHtml = `
        <div id="preorderDetailsModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 30px; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; color: #333;">Pre-Order Details #${preorder.id}</h3>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div>
                        <strong>Customer:</strong><br>
                        ${preorder.first_name} ${preorder.last_name}<br>
                        <small style="color: #666;">${preorder.email}</small><br>
                        <small style="color: #666;">${preorder.phone || 'No phone'}</small>
                    </div>
                    <div>
                        <strong>Status:</strong><br>
                        <span style="background-color: ${getStatusColor(preorder.status)}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                            ${preorder.status.charAt(0).toUpperCase() + preorder.status.slice(1)}
                        </span>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <strong>Product:</strong> ${preorder.product_name}<br>
                    <strong>Quantity:</strong> ${preorder.quantity}<br>
                    <strong>Expected Price:</strong> $${parseFloat(preorder.expected_price || 0).toFixed(2)}<br>
                    <strong>Deposit Amount:</strong> $${parseFloat(preorder.deposit_amount || 0).toFixed(2)}<br>
                    <strong>Total Paid:</strong> $${parseFloat(preorder.total_paid || 0).toFixed(2)}
                </div>

                <div style="margin-bottom: 20px;">
                    <strong>Created:</strong> ${new Date(preorder.created_date).toLocaleDateString()}<br>
                    <strong>Updated:</strong> ${new Date(preorder.updated_date).toLocaleDateString()}
                </div>

                <div style="text-align: right;">
                    <button onclick="closePreOrderDetailsModal()" style="background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Close</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closePreOrderDetailsModal() {
    const modal = document.getElementById('preorderDetailsModal');
    if (modal) {
        modal.remove();
    }
}

function getStatusColor(status) {
    const colors = {
        'pending': '#ffc107',
        'confirmed': '#17a2b8',
        'ready': '#28a745',
        'completed': '#28a745',  // Green for completed
        'cancelled': '#dc3545'
    };
    return colors[status] || '#6c757d';
}
