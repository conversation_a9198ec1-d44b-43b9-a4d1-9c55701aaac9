#!/usr/bin/env python3
"""
Test script to verify that marking pre-orders as ready sends notifications
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import PreOrder, Notification, get_db
import mysql.connector

def test_ready_notification():
    """Test that marking a pre-order as ready sends a notification"""
    app = create_app()
    
    with app.app_context():
        try:
            # Get a confirmed pre-order to test with
            conn = get_db()
            cur = conn.cursor(dictionary=True)
            
            # Find a confirmed pre-order
            cur.execute("""
                SELECT po.id, po.customer_id, p.name as product_name, po.quantity
                FROM pre_orders po
                JOIN products p ON po.product_id = p.id
                WHERE po.status = 'confirmed'
                LIMIT 1
            """)
            
            preorder = cur.fetchone()
            
            if not preorder:
                print("❌ No confirmed pre-orders found to test with")
                return False
            
            print(f"🔍 Testing with pre-order #{preorder['id']} for customer {preorder['customer_id']}")
            print(f"   Product: {preorder['product_name']} (Qty: {preorder['quantity']})")
            
            # Count notifications before
            cur.execute("SELECT COUNT(*) as count FROM notifications WHERE customer_id = %s", 
                       (preorder['customer_id'],))
            notifications_before = cur.fetchone()['count']
            print(f"📧 Notifications before: {notifications_before}")
            
            # Mark as ready for pickup
            print("🔄 Marking pre-order as ready for pickup...")
            PreOrder.mark_ready_for_pickup(preorder['id'])
            
            # Count notifications after
            cur.execute("SELECT COUNT(*) as count FROM notifications WHERE customer_id = %s", 
                       (preorder['customer_id'],))
            notifications_after = cur.fetchone()['count']
            print(f"📧 Notifications after: {notifications_after}")
            
            # Check if notification was created
            if notifications_after > notifications_before:
                # Get the latest notification
                cur.execute("""
                    SELECT message, notification_type, related_id, created_date
                    FROM notifications 
                    WHERE customer_id = %s 
                    ORDER BY created_date DESC 
                    LIMIT 1
                """, (preorder['customer_id'],))
                
                latest_notification = cur.fetchone()
                print("✅ SUCCESS: Notification created!")
                print(f"   Type: {latest_notification['notification_type']}")
                print(f"   Message: {latest_notification['message']}")
                print(f"   Related ID: {latest_notification['related_id']}")
                print(f"   Created: {latest_notification['created_date']}")
                return True
            else:
                print("❌ FAILED: No notification was created")
                return False
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            return False
        finally:
            cur.close()
            conn.close()

if __name__ == "__main__":
    print("🧪 Testing pre-order ready notification functionality...")
    print("=" * 60)
    
    success = test_ready_notification()
    
    print("=" * 60)
    if success:
        print("🎉 TEST PASSED: Ready notifications are working!")
    else:
        print("💥 TEST FAILED: Ready notifications are not working")
    
    sys.exit(0 if success else 1)
